# Design Document

## Overview

This design outlines a comprehensive end-to-end testing system that replaces the current fragmented testing approach with two focused test suites: dedicated API integration tests and full user journey tests. The system will eliminate mock-heavy tests in favor of real data validation and actual user flow testing.

## Architecture

### Testing Architecture Overview

```mermaid
graph TB
    subgraph "Test Cleanup Phase"
        A[Remove Existing Tests] --> B[Clean Test Directories]
        B --> C[Remove Mock-Based Tests]
    end
    
    subgraph "API Testing Suite"
        D[Hub Server Required] --> E[REST API Tests]
        E --> F[Real Database Integration]
        F --> G[Schema Validation]
    end
    
    subgraph "E2E Testing Suite"
        H[Playwright Tests] --> I[Real User Flows]
        I --> J[Authentication Flow]
        J --> K[Import Flow]
        K --> L[Display Flow]
    end
    
    subgraph "CI/CD Integration"
        M[Environment Check] --> N[Service Health Check]
        N --> O[Basic Startup Validation]
    end
    
    C --> D
    C --> H
    G --> M
    L --> M
```

### Test Suite Structure

```
tests/
├── api/                          # API Integration Tests
│   ├── auth.api.test.ts         # Authentication endpoints
│   ├── trips.api.test.ts        # Trip management endpoints
│   ├── import.api.test.ts       # AI import endpoints
│   ├── places.api.test.ts       # Places integration endpoints
│   ├── public.api.test.ts       # Public access endpoints
│   └── utils/
│       ├── api-client.ts        # HTTP client for tests
│       ├── test-data.ts         # Real test data
│       └── setup.ts             # API test setup
├── e2e/                         # End-to-End Tests
│   ├── user-journey.spec.ts     # Complete user flow
│   ├── auth-flow.spec.ts        # Authentication flows
│   ├── import-flow.spec.ts      # AI import flows
│   ├── trip-display.spec.ts     # Trip visualization
│   └── utils/
│       ├── test-accounts.ts     # Real user accounts
│       ├── test-content.ts      # AI conversation samples
│       └── page-objects.ts      # Page object models
└── ci/                          # CI/CD Tests
    ├── health-check.test.ts     # Service health validation
    ├── env-validation.test.ts   # Environment setup check
    └── startup.test.ts          # Service startup validation
```

## Components and Interfaces

### API Testing Components

#### API Client Interface
```typescript
interface ApiTestClient {
  // Authentication with Token Management
  signup(credentials: SignupData): Promise<AuthResponse>;
  login(credentials: LoginData): Promise<AuthResponse>;
  logout(): Promise<void>;
  
  // Token Management
  getStoredToken(): string | null;
  setStoredToken(token: string, refreshToken: string): void;
  clearStoredToken(): void;
  refreshTokenIfExpired(): Promise<void>;
  isTokenExpired(token: string): boolean;
  
  // Authenticated Requests (auto-handles token refresh)
  authenticatedRequest<T>(method: string, endpoint: string, data?: any): Promise<T>;
  
  // Trip Management
  createTrip(tripData: CreateTripData): Promise<TripResponse>;
  getTrips(params?: GetTripsParams): Promise<TripsResponse>;
  updateTrip(id: string, updates: UpdateTripData): Promise<TripResponse>;
  deleteTrip(id: string): Promise<void>;
  
  // Import Operations
  parseContent(content: string, source: string): Promise<ImportResponse>;
  getImportStatus(sessionId: string): Promise<ImportStatusResponse>;
  createTripFromImport(sessionId: string, edits?: TripEdits): Promise<TripResponse>;
  
  // Validation
  validateResponse<T>(response: any, schema: ZodSchema<T>): T;
  expectSuccess(response: ApiResponse): void;
  expectError(response: ApiResponse, expectedCode: number): void;
}
```

#### Test Data Management
```typescript
interface TestDataManager {
  // User Management
  createTestUser(): Promise<TestUser>;
  cleanupTestUser(userId: string): Promise<void>;
  
  // Trip Data
  generateTripData(): CreateTripData;
  generateImportContent(): ImportTestContent;
  
  // Validation Data
  getValidationSchemas(): ValidationSchemas;
  
  // Cleanup
  cleanupTestData(): Promise<void>;
}
```

### E2E Testing Components

#### Page Object Models
```typescript
interface HomePage {
  navigate(): Promise<void>;
  clickLogin(): Promise<void>;
  clickSignup(): Promise<void>;
  isLoaded(): Promise<boolean>;
}

interface LoginPage {
  login(email: string, password: string): Promise<void>;
  expectLoginSuccess(): Promise<void>;
  expectLoginError(message: string): Promise<void>;
}

interface ImportPage {
  pasteContent(content: string): Promise<void>;
  selectSource(source: string): Promise<void>;
  startImport(): Promise<void>;
  waitForImportComplete(): Promise<void>;
  expectImportSuccess(): Promise<void>;
}

interface TripDisplayPage {
  expectTripLoaded(): Promise<void>;
  expectTimelineVisible(): Promise<void>;
  expectMapVisible(): Promise<void>;
  expectActivitiesCount(count: number): Promise<void>;
  dragActivity(from: number, to: number): Promise<void>;
}
```

#### User Flow Manager
```typescript
interface UserFlowManager {
  // Complete Flows
  executeCompleteUserJourney(): Promise<void>;
  executeAuthenticationFlow(): Promise<void>;
  executeImportFlow(): Promise<void>;
  
  // Flow Steps
  navigateToHome(): Promise<void>;
  performLogin(): Promise<void>;
  importTravelContent(): Promise<void>;
  verifyTripDisplay(): Promise<void>;
  
  // Validation
  captureScreenshot(name: string): Promise<void>;
  expectPageTitle(title: string): Promise<void>;
  expectElementVisible(selector: string): Promise<void>;
}
```

## Data Models

### Test Configuration
```typescript
interface TestConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retries: number;
  };
  e2e: {
    baseUrl: string;
    headless: boolean;
    viewport: { width: number; height: number };
    timeout: number;
  };
  database: {
    testDbUrl: string;
    cleanupAfterTests: boolean;
  };
  auth: {
    testUserEmail: string;
    testUserPassword: string;
  };
}
```

### Test Data Models
```typescript
interface TestUser {
  id: string;
  email: string;
  password: string;
  accessToken?: string;
  refreshToken?: string;
}

interface ImportTestContent {
  content: string;
  source: 'chatgpt' | 'claude' | 'gemini';
  expectedTrip: {
    title: string;
    destinations: string[];
    activityCount: number;
  };
}

interface ApiTestResult {
  endpoint: string;
  method: string;
  success: boolean;
  responseTime: number;
  statusCode: number;
  error?: string;
}
```

## Error Handling

### API Test Error Handling
- **Connection Errors**: Retry with exponential backoff
- **Authentication Failures**: Clear tokens and re-authenticate
- **Rate Limiting**: Implement proper delays between requests
- **Schema Validation Errors**: Capture detailed validation failures
- **Database Errors**: Ensure proper cleanup and rollback

### E2E Test Error Handling
- **Page Load Failures**: Retry navigation with timeout handling
- **Element Not Found**: Wait with explicit timeouts and retry logic
- **Authentication Issues**: Clear browser state and re-authenticate
- **Import Failures**: Capture detailed error states and screenshots
- **Network Issues**: Implement proper wait strategies

### Error Reporting
```typescript
interface TestErrorReport {
  testName: string;
  errorType: 'api' | 'e2e' | 'setup';
  error: string;
  stackTrace: string;
  screenshot?: string;
  requestDetails?: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
  };
  responseDetails?: {
    status: number;
    headers: Record<string, string>;
    body?: any;
  };
}
```

## Testing Strategy

### API Testing Strategy
1. **Server Dependency**: All API tests require the hub server to be running on port 3001
2. **Real Database**: Tests use actual Supabase connections with test data
3. **Schema Validation**: Every response validated against Zod schemas
4. **Authentication Flow**: Real JWT tokens and Supabase auth
5. **Data Persistence**: Verify actual database operations
6. **Cleanup**: Proper test data cleanup after each test

### E2E Testing Strategy
1. **Real User Accounts**: Use actual Supabase user accounts for testing
2. **Complete Flows**: Test entire user journeys from start to finish
3. **Visual Validation**: Screenshot comparison for UI elements
4. **Interactive Testing**: Real drag-and-drop, form submissions, navigation
5. **Cross-Browser**: Test on Chrome, Firefox, Safari, and mobile viewports
6. **Performance**: Monitor page load times and interaction responsiveness

### Test Data Strategy
1. **Real Content**: Use actual AI-generated travel conversations
2. **Diverse Scenarios**: Multiple trip types, destinations, and complexity levels
3. **Edge Cases**: Test with malformed content, empty responses, large datasets
4. **Cleanup**: Automated cleanup of test trips and user data
5. **Isolation**: Each test creates its own data to avoid conflicts

## Implementation Phases

### Phase 1: Complete Test Cleanup and Foundation
1. **Archive Existing Tests**: Move all current test files to `test-archive/` folder
2. **Clean Directories**: Remove all test-related directories and files we no longer need
3. **Environment Setup**: Ensure tests use `.env.local` files for configuration
4. **Create Clean Structure**: Set up new `tests/` directory with proper organization

### Phase 2: Perfect Foundation Tests
1. **Authentication API Test**: Create perfect auth test that demonstrates:
   - Real login/signup flow with `.env.local` credentials
   - Token persistence and reuse across tests
   - Automatic token refresh when expired
   - Clean token cleanup and renewal
2. **Single Protected API Test**: Create one perfect protected endpoint test that demonstrates:
   - Reusing saved authentication tokens
   - Real server connection (requires `pnpm run dev` hub running)
   - Proper error handling and reporting
   - Clean setup and teardown
3. **Single E2E Test**: Create one perfect E2E test (homepage → login flow) that demonstrates:
   - Real browser automation with authentication
   - Environment configuration from `.env.local`
   - Screenshot capture on failure
   - Clean test isolation

### Phase 3: Validate and Perfect Foundation
1. **Test the Foundation**: Ensure both foundation tests work flawlessly
2. **Optimize Configuration**: Perfect the test setup, environment handling, and reporting
3. **Document Patterns**: Create clear patterns that can be copied for all other tests
4. **Simplify and Clean**: Remove any complexity, focus on maximum efficiency

### Phase 4: Scale the Pattern
1. **Copy API Pattern**: Use the perfect API test as template for all other endpoints
2. **Copy E2E Pattern**: Use the perfect E2E test as template for all user flows
3. **Systematic Implementation**: Build out comprehensive coverage using proven patterns
4. **CI/CD Integration**: Add simple environment and startup validation

## Performance Considerations

### API Test Performance
- **Parallel Execution**: Run independent API tests in parallel
- **Connection Pooling**: Reuse HTTP connections where possible
- **Rate Limiting**: Respect API rate limits with proper delays
- **Database Optimization**: Use transactions for test data setup/cleanup

### E2E Test Performance
- **Browser Reuse**: Share browser instances across related tests
- **Selective Testing**: Run full E2E suite only on critical changes
- **Parallel Browsers**: Run tests across multiple browser instances
- **Smart Waiting**: Use explicit waits instead of arbitrary delays

### Resource Management
- **Memory Usage**: Monitor and limit memory consumption during tests
- **Database Connections**: Proper connection cleanup and pooling
- **File System**: Clean up temporary files and screenshots
- **Network Resources**: Efficient HTTP client configuration