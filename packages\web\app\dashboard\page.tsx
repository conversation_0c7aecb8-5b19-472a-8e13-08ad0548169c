'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Search, Filter, CalendarDays, MapPin, ChevronLeft, ChevronRight } from 'lucide-react';
import { useAuthStore } from '@/stores/auth.store';
import { useTripStore } from '@/stores/trip.store';
import { useUIStore } from '@/stores/ui.store';
import { toast } from 'sonner';
import { mapErrorMessage } from '@/lib/error-messages';
import { TripCard } from '@/components/dashboard/TripCard';
import { CreateTripModal } from '@/components/dashboard/CreateTripModal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { DynamicNumberTicker } from '@/components/magic-ui/number-ticker-dynamic';
import { BentoDashboard } from '@/components/dashboard/BentoDashboard';

export default function DashboardPage() {
  const [showBentoView, setShowBentoView] = useState(true);
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { 
    trips, 
    fetchTripsPaginated, 
    createTrip, 
    isLoading, 
    error,
    currentPage,
    totalPages,
    totalTrips,
    hasNextPage,
    hasPrevPage,
    pageSize
  } = useTripStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedTrip, setSelectedTrip] = useState<string | null>(null);
  const [isCreateTripModalOpen, setIsCreateTripModalOpen] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    // Fetch trips with pagination when component mounts
    fetchTripsPaginated(1, pageSize).catch((err) => {
      toast.error('Failed to load trips', {
        description: mapErrorMessage(err.message || '')
      });
    });
  }, [isAuthenticated, router, pageSize]); // Remove fetchTripsPaginated from dependencies

  // Filter trips based on search and status
  const filteredTrips = trips.filter((trip) => {
    const matchesSearch = trip.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (trip.destination?.toLowerCase().includes(searchQuery.toLowerCase()) || false);
    
    if (filterStatus === 'all') return matchesSearch;
    
    // Use backend status as single source of truth
    const status = trip.status;
    
    // Map backend status values to filter values
    const filterMapping: Record<string, string[]> = {
      'upcoming': ['draft', 'planning', 'confirmed'],
      'in-progress': ['in_progress'],
      'completed': ['completed', 'cancelled']
    };
    
    const matchingStatuses = filterMapping[filterStatus] || [filterStatus];
    return matchesSearch && matchingStatuses.includes(status);
  });

  // Transform trip data for TripCard component
  const transformedTrips = filteredTrips.map((trip) => {
    // Map backend status to display status
    const getDisplayStatus = (backendStatus: string): 'upcoming' | 'in-progress' | 'completed' => {
      switch (backendStatus) {
        case 'in_progress':
          return 'in-progress';
        case 'completed':
        case 'cancelled':
          return 'completed';
        case 'draft':
        case 'planning':
        case 'confirmed':
        default:
          return 'upcoming';
      }
    };
    
    return {
      id: trip.id,
      title: trip.title,
      destination: trip.destination || 'Unknown',
      status: getDisplayStatus(trip.status),
      imageUrl: trip.cover_image || '/images/placeholder-trip.jpg',
      viewCount: trip.views || 0,
      activities: trip.activities?.length || 0,
      startDate: trip.start_date || new Date().toISOString(),
      endDate: trip.end_date || new Date().toISOString(),
    };
  });

  const handleCreateTrip = () => {
    setIsCreateTripModalOpen(true);
  };

  if (isLoading && trips.length === 0) {
    return <DashboardSkeleton />;
  }

  // Show Bento Dashboard for a modern view
  if (showBentoView) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="py-8">
          <BentoDashboard />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Welcome back{user?.name ? `, ${user.name}` : ''}!
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Plan your next adventure or revisit your memories
          </p>
        </div>

        {/* Actions Bar */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search trips..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All trips</SelectItem>
              <SelectItem value="upcoming">Upcoming</SelectItem>
              <SelectItem value="in-progress">In progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleCreateTrip}>
            <Plus className="h-4 w-4 mr-2" />
            New Trip
          </Button>
        </div>

        {/* Trips Grid */}
        {error && (
          <div className="text-center py-8 text-red-600 dark:text-red-400">
            {error}
          </div>
        )}

        {filteredTrips.length === 0 ? (
          <EmptyState onCreateTrip={handleCreateTrip} hasTrips={trips.length > 0} />
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {transformedTrips.map((trip) => (
                <TripCard
                  key={trip.id}
                  trip={trip}
                  isSelected={selectedTrip === trip.id}
                  onSelect={() => setSelectedTrip(trip.id)}
                />
              ))}
            </div>
            
            {/* Pagination Controls */}
            {totalPages > 0 && (
              <div className="mt-8 border-t pt-8">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  {/* Page size selector */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Show</span>
                    <Select 
                      value={pageSize.toString()} 
                      onValueChange={(value) => fetchTripsPaginated(1, parseInt(value))}
                    >
                      <SelectTrigger className="w-[80px] h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                    <span className="text-sm text-gray-600 dark:text-gray-400">per page</span>
                  </div>
                  
                  {/* Pagination navigation */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => fetchTripsPaginated(currentPage - 1, pageSize)}
                      disabled={!hasPrevPage || isLoading}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    
                    <div className="flex items-center gap-1">
                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }
                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === currentPage ? "default" : "outline"}
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => fetchTripsPaginated(pageNum, pageSize)}
                            disabled={isLoading}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => fetchTripsPaginated(currentPage + 1, pageSize)}
                      disabled={!hasNextPage || isLoading}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {/* Trip count with animated numbers */}
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Showing{' '}
                    <DynamicNumberTicker 
                      value={(currentPage - 1) * pageSize + 1} 
                      springConfig="quick"
                      className="font-semibold"
                    />
                    -
                    <DynamicNumberTicker 
                      value={Math.min(currentPage * pageSize, totalTrips)} 
                      springConfig="quick"
                      className="font-semibold"
                    />
                    {' '}of{' '}
                    <DynamicNumberTicker 
                      value={totalTrips} 
                      springConfig="quick"
                      className="font-semibold"
                    />
                    {' '}trips
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Create Trip Modal */}
      <CreateTripModal
        isOpen={isCreateTripModalOpen}
        onClose={() => setIsCreateTripModalOpen(false)}
        onCreateTrip={async (tripData) => {
          try {
            const newTrip = await createTrip(tripData);
            toast.success('Trip created!', {
              description: `${newTrip.title} has been created successfully.`
            });
            setIsCreateTripModalOpen(false);
            // Navigate to the trip editor
            router.push(`/plan/${newTrip.id}`);
          } catch (error) {
            toast.error('Failed to create trip', {
              description: mapErrorMessage(error instanceof Error ? error.message : '')
            });
          }
        }}
      />
    </div>
  );
}

function EmptyState({ onCreateTrip, hasTrips }: { onCreateTrip: () => void; hasTrips: boolean }) {
  return (
    <Card className="border-dashed">
      <CardContent className="flex flex-col items-center justify-center py-16 px-8 text-center">
        <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-6 mb-6">
          <MapPin className="h-8 w-8 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {hasTrips ? 'No trips found' : 'No trips yet'}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-sm">
          {hasTrips 
            ? 'Try adjusting your search or filters to find what you\'re looking for.'
            : 'Start planning your first adventure. Create a trip to get started!'}
        </p>
        <Button onClick={onCreateTrip}>
          <Plus className="h-4 w-4 mr-2" />
          Create your first trip
        </Button>
      </CardContent>
    </Card>
  );
}

function DashboardSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <Skeleton className="h-9 w-64 mb-2" />
          <Skeleton className="h-5 w-96" />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <Skeleton className="flex-1 h-10" />
          <Skeleton className="w-full sm:w-[180px] h-10" />
          <Skeleton className="w-32 h-10" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Skeleton key={i} className="h-64 rounded-lg" />
          ))}
        </div>
      </div>
    </div>
  );
}