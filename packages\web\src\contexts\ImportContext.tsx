'use client';

import { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import type { ParsedTrip, ParsedActivity } from '@travelviz/shared';
import { useImportAnalytics } from '@/hooks/useImportAnalytics';

export type ImportStep = 'input' | 'parsing' | 'preview' | 'creating';
export type ImportSource = 'paste' | 'file' | 'url';

interface ParseProgress {
  step: string;
  progress: number;
  message: string;
}

interface ImportContextType {
  // State
  currentStep: ImportStep;
  source: ImportSource;
  content: string;
  uploadedFile: File | null;
  parsedTrip: ParsedTrip | null;
  parseProgress: ParseProgress | null;
  importId: string | null;
  error: string | null;
  
  // Actions
  setStep: (step: ImportStep) => void;
  setSource: (source: ImportSource) => void;
  setContent: (content: string) => void;
  setUploadedFile: (file: File | null) => void;
  setParsedTrip: (trip: ParsedTrip | null) => void;
  setParseProgress: (progress: ParseProgress | null) => void;
  setImportId: (id: string | null) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

const ImportContext = createContext<ImportContextType | undefined>(undefined);

export function ImportProvider({ children }: { children: ReactNode }) {
  const [currentStep, setCurrentStep] = useState<ImportStep>('input');
  const [source, setSource] = useState<ImportSource>('paste');
  const [content, setContent] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [parsedTrip, setParsedTrip] = useState<ParsedTrip | null>(null);
  const [parseProgress, setParseProgress] = useState<ParseProgress | null>(null);
  const [importId, setImportId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { trackStepChange } = useImportAnalytics();

  // Wrap setStep to track analytics
  const setStep = useCallback((newStep: ImportStep) => {
    trackStepChange(currentStep, newStep);
    setCurrentStep(newStep);
  }, [currentStep, trackStepChange]);

  const reset = () => {
    setStep('input');
    setSource('paste');
    setContent('');
    setUploadedFile(null);
    setParsedTrip(null);
    setParseProgress(null);
    setImportId(null);
    setError(null);
  };

  return (
    <ImportContext.Provider value={{
      currentStep,
      source,
      content,
      uploadedFile,
      parsedTrip,
      parseProgress,
      importId,
      error,
      setStep,
      setSource,
      setContent,
      setUploadedFile,
      setParsedTrip,
      setParseProgress,
      setImportId,
      setError,
      reset
    }}>
      {children}
    </ImportContext.Provider>
  );
}

export function useImport() {
  const context = useContext(ImportContext);
  if (!context) {
    throw new Error('useImport must be used within ImportProvider');
  }
  return context;
}