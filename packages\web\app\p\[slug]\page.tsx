"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  MapPin, 
  Calendar, 
  Clock, 
  DollarSign, 
  Eye, 
  Share2, 
  Copy, 
  Download,
  Heart,
  Star,
  User,
  ArrowRight,
  ExternalLink,
  Loader2,
  AlertCircle
} from 'lucide-react';
import dynamic from 'next/dynamic';
import { api } from '@/lib/api-client';
import { useAuthStore } from '@/stores/auth.store';
import { useToast } from '@/hooks/use-toast';
import { format, differenceInDays } from 'date-fns';
import { type Activity, ActivityType } from '@travelviz/shared';

// Dynamic imports for better bundle splitting
const DynamicTripMap = dynamic(() => import('@/components/DynamicTripMap'), {
  ssr: false,
  loading: () => <div className="h-96 bg-gray-100 animate-pulse rounded-lg" />
});

const MotionDiv = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.motion.div })),
  { ssr: false }
);
import { formatCurrency } from '@/lib/utils';

interface PublicTripData {
  trip: {
    id: string;
    title: string;
    description?: string | null;
    destination?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    coverImage?: string | null;
    tags?: string[];
    budgetAmount?: number | null;
    budgetCurrency?: string | null;
    createdAt: string;
    updatedAt: string;
    authorName: string;
    start_date?: string | null;
    end_date?: string | null;
    budget_amount?: number | null;
    budget_currency?: string | null;
    created_at: string;
    updated_at: string;
  };
  activities: Activity[];
  viewCount: number;
}

export default function PublicTripPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const router = useRouter();
  const { user } = useAuthStore();
  const { toast } = useToast();
  const [tripData, setTripData] = useState<PublicTripData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDay, setSelectedDay] = useState<number>(1);
  const [isLiked, setIsLiked] = useState(false);
  const [copying, setCopying] = useState(false);

  useEffect(() => {
    fetchTripData();
  }, [slug]);

  const fetchTripData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.public.getTrip(slug);
      if (response.success && response.trip) {
        setTripData({
          trip: {
            ...response.trip,
            authorName: response.trip.user.name || 'Anonymous',
            createdAt: response.trip.created_at,
            updatedAt: response.trip.updated_at,
            startDate: response.trip.start_date || undefined,
            endDate: response.trip.end_date || undefined,
            coverImage: response.trip.cover_image || undefined,
            budgetAmount: response.trip.budget_amount || undefined,
            budgetCurrency: response.trip.budget_currency || undefined,
          },
          activities: response.trip.activities || [],
          viewCount: response.trip.views || 0,
        });
      }
    } catch (err) {
      setError('This trip is not available or has been made private.');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyTrip = async () => {
    if (!user) {
      router.push(`/signup?redirect=/p/${slug}`);
      return;
    }

    try {
      setCopying(true);
      
      // Clone the trip
      const response = await api.trips.clone(tripData!.trip.id);
      
      toast({
        title: 'Trip copied!',
        description: `Trip '${response.trip.title}' has been copied to your account.`,
      });
      
      // Redirect to the new trip
      router.push(`/plan/${response.trip.id}`);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to copy trip. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setCopying(false);
    }
  };

  const handleShare = async () => {
    const shareUrl = window.location.href;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: tripData?.trip.title,
          text: tripData?.trip.description || undefined,
          url: shareUrl,
        });
      } catch (err) {
        // User cancelled sharing
      }
    } else {
      // Fallback to clipboard
      await navigator.clipboard.writeText(shareUrl);
      toast({
        title: 'Link copied!',
        description: 'Trip link has been copied to your clipboard.',
      });
    }
  };

  // Group activities by day
  const groupActivitiesByDay = () => {
    if (!tripData) return [];
    
    const grouped: { [key: string]: Activity[] } = {};
    
    tripData.activities.forEach(activity => {
      if (activity.start_time) {
        const day = format(new Date(activity.start_time), 'yyyy-MM-dd');
        if (!grouped[day]) {
          grouped[day] = [];
        }
        grouped[day].push(activity);
      }
    });

    // Sort days and activities within each day
    return Object.entries(grouped)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([date, activities], index) => ({
        day: index + 1,
        date,
        activities: activities.sort((a, b) => 
          new Date(a.start_time!).getTime() - new Date(b.start_time!).getTime()
        ),
      }));
  };

  const getDuration = () => {
    if (!tripData?.trip.startDate || !tripData?.trip.endDate) return 0;
    return differenceInDays(
      new Date(tripData.trip.endDate),
      new Date(tripData.trip.startDate)
    ) + 1;
  };

  const getTotalCost = () => {
    if (!tripData) return 0;
    return tripData.activities.reduce((sum, activity) => sum + (activity.price || 0), 0);
  };

  const getActivityIcon = (type: string) => {
    const icons: Record<string, string> = {
      'flight': '✈️',
      'accommodation': '🏨',
      'transport': '🚗',
      'dining': '🍽️',
      'activity': '🎯',
      'shopping': '🛍️',
      'car_rental': '🚗',
      'tour': '🗺️',
      'sightseeing': '👁️',
      'entertainment': '🎭',
      'other': '📌',
    };
    return icons[type] || '📌';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading trip...</p>
        </div>
      </div>
    );
  }

  if (error || !tripData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md w-full">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Trip Not Found</h2>
            <p className="text-gray-600 mb-6">{error || 'This trip is not available.'}</p>
            <Button onClick={() => router.push('/')}>
              Go to Homepage
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  const days = groupActivitiesByDay();
  const duration = getDuration();
  const totalCost = getTotalCost();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <div className="lg:col-span-2">
              {tripData.trip.tags && tripData.trip.tags.length > 0 && (
                <div className="flex items-center space-x-2 mb-4">
                  {tripData.trip.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="bg-white/20 text-white border-white/30">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
              
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{tripData.trip.title}</h1>
              {tripData.trip.description && (
                <p className="text-xl text-white/90 mb-6">{tripData.trip.description}</p>
              )}
              
              <div className="flex items-center space-x-6 text-white/80">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{duration} days</span>
                </div>
                {tripData.trip.destination && (
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span>{tripData.trip.destination}</span>
                  </div>
                )}
                <div className="flex items-center space-x-1">
                  <Eye className="h-4 w-4" />
                  <span>{tripData.viewCount.toLocaleString()} views</span>
                </div>
              </div>
            </div>

            <div className="lg:col-span-1">
              <Card className="p-6 bg-white/10 backdrop-blur-sm border-white/20">
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                      <User className="h-6 w-6" />
                    </div>
                    <div className="text-left">
                      <div className="font-semibold">{tripData.trip.authorName}</div>
                      <p className="text-sm text-white/70">Trip Creator</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">
                        {formatCurrency(totalCost, tripData.trip.budgetCurrency || 'USD')}
                      </div>
                      <div className="text-sm text-white/70">Est. Budget</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{tripData.activities.length}</div>
                      <div className="text-sm text-white/70">Activities</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Button 
                      onClick={handleCopyTrip}
                      disabled={copying}
                      className="w-full bg-white text-orange-500 hover:bg-gray-100"
                    >
                      {copying ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Copying...
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy to My Account
                        </>
                      )}
                    </Button>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 border-white/30 text-white hover:bg-white/10"
                        onClick={() => setIsLiked(!isLiked)}
                      >
                        <Heart className={`h-4 w-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                        Like
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 border-white/30 text-white hover:bg-white/10"
                        onClick={handleShare}
                      >
                        <Share2 className="h-4 w-4 mr-1" />
                        Share
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Map Section */}
          <div className="lg:col-span-2">
            <Card className="p-6 mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Trip Map</h2>
              <div className="h-96">
                <DynamicTripMap activities={tripData.activities.filter(a => a.trip_id).map(a => ({
                  ...a,
                  trip_id: a.trip_id!,
                  type: a.type as Activity['type']
                } as Activity))} />
              </div>
            </Card>
          </div>

          {/* Timeline Section */}
          <div className="space-y-6">
            <Card className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Itinerary</h3>
              
              {days.length > 0 ? (
                <>
                  <div className="flex space-x-2 mb-4 overflow-x-auto">
                    {days.map((day) => (
                      <button
                        key={day.day}
                        onClick={() => setSelectedDay(day.day)}
                        className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                          selectedDay === day.day
                            ? 'bg-orange-500 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        Day {day.day}
                      </button>
                    ))}
                  </div>

                  {days.map((day) => {
                    if (day.day !== selectedDay) return null;
                    
                    return (
                      <div key={day.day} className="space-y-3">
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600">
                            {format(new Date(day.date), 'EEEE, MMMM d, yyyy')}
                          </p>
                        </div>

                        {day.activities.map((activity, index) => (
                          <MotionDiv
                            key={activity.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="bg-white border border-gray-200 rounded-lg p-4"
                          >
                            <div className="flex items-start space-x-3">
                              <div className="text-2xl flex-shrink-0">
                                {getActivityIcon(activity.type)}
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900">{activity.title}</h4>
                                {activity.location && (
                                  <p className="text-sm text-gray-600 mt-1">
                                    <MapPin className="h-3 w-3 inline mr-1" />
                                    {activity.location}
                                  </p>
                                )}
                                {activity.start_time && (
                                  <p className="text-sm text-gray-600 mt-1">
                                    <Clock className="h-3 w-3 inline mr-1" />
                                    {format(new Date(activity.start_time), 'h:mm a')}
                                  </p>
                                )}
                                {activity.price && activity.price > 0 && (
                                  <p className="text-sm text-gray-600 mt-1">
                                    <DollarSign className="h-3 w-3 inline mr-1" />
                                    {formatCurrency(activity.price, activity.currency)}
                                  </p>
                                )}
                                {activity.description && (
                                  <p className="text-sm text-gray-700 mt-2">
                                    {activity.description}
                                  </p>
                                )}
                                {activity.booking_url && (
                                  <a
                                    href={activity.booking_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-flex items-center text-sm text-orange-500 hover:text-orange-600 mt-2"
                                  >
                                    Book Now
                                    <ExternalLink className="h-3 w-3 ml-1" />
                                  </a>
                                )}
                              </div>
                            </div>
                          </MotionDiv>
                        ))}
                      </div>
                    );
                  })}
                </>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No scheduled activities yet</p>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}