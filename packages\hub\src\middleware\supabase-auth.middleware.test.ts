import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import express, { Request, Response, NextFunction } from 'express';
import { authenticateSupabaseUser, optionalSupabaseAuth, SupabaseAuthenticatedRequest } from './supabase-auth.middleware';
import { verifySupabaseJWT, SupabaseJWTError } from '../utils/supabase-jwt';
import { verifyAccessToken, TokenError } from '../utils/tokens';
import { HTTP_STATUS } from '@travelviz/shared';

// Mock dependencies with proper module handling
vi.mock('../utils/supabase-jwt', async (importOriginal) => {
  const original = await importOriginal<typeof import('../utils/supabase-jwt')>();
  return {
    ...original, // Keep original SupabaseJWTError and other exports
    verifySupabaseJWT: vi.fn(),
    extractBearerToken: original.extractBearerToken, // Use real implementation
  };
});

vi.mock('../utils/tokens', async (importOriginal) => {
  const original = await importOriginal<typeof import('../utils/tokens')>();
  return {
    ...original, // Keep original TokenError
    verifyAccessToken: vi.fn(),
  };
});

vi.mock('../utils/logger');

describe('Supabase Auth Middleware - Cascade Prevention Tests', () => {
  let req: any;
  let res: any;
  let next: any;

  beforeEach(() => {
    req = {
      headers: {},
      user: undefined,
    };
    res = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
    };
    next = vi.fn();
    
    vi.clearAllMocks();
  });

  describe('authenticateSupabaseUser - Critical Auth Flow', () => {
    it('should prevent cascade when JWT verification fails', async () => {
      // Simulate the root cause: JWT verification failure
      req.headers.authorization = 'Bearer invalid-token';
      vi.mocked(verifySupabaseJWT).mockRejectedValueOnce(new SupabaseJWTError('Invalid token'));

      await authenticateSupabaseUser(req, res, next);

      // Must return 401 without calling next()
      expect(res.status).toHaveBeenCalledWith(HTTP_STATUS.UNAUTHORIZED);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.any(String),
        })
      );
      expect(next).not.toHaveBeenCalled();
    });

    it('should handle successful JWT verification', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      req.headers.authorization = 'Bearer valid-token';
      vi.mocked(verifySupabaseJWT).mockResolvedValueOnce(mockUser);

      await authenticateSupabaseUser(req, res, next);

      expect(req.user).toEqual(mockUser);
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should return 401 immediately for missing token', async () => {
      // No authorization header
      await authenticateSupabaseUser(req, res, next);

      expect(res.status).toHaveBeenCalledWith(HTTP_STATUS.UNAUTHORIZED);
      expect(verifySupabaseJWT).not.toHaveBeenCalled(); // No verification attempt
      expect(next).not.toHaveBeenCalled();
    });

    it('should handle network errors gracefully', async () => {
      req.headers.authorization = 'Bearer token';
      const networkError = new Error('ECONNREFUSED');
      vi.mocked(verifySupabaseJWT).mockRejectedValueOnce(networkError);

      await authenticateSupabaseUser(req, res, next);

      expect(res.status).toHaveBeenCalledWith(HTTP_STATUS.INTERNAL_SERVER_ERROR);
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('optionalSupabaseAuth - Graceful Degradation', () => {
    it('should continue without user when token is invalid', async () => {
      req.headers.authorization = 'Bearer invalid-token';
      vi.mocked(verifySupabaseJWT).mockRejectedValueOnce(new Error('Invalid'));

      await optionalSupabaseAuth(req, res, next);

      expect(req.user).toBeUndefined();
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should add user when token is valid', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      req.headers.authorization = 'Bearer valid-token';
      vi.mocked(verifySupabaseJWT).mockResolvedValueOnce(mockUser);

      await optionalSupabaseAuth(req, res, next);

      expect(req.user).toEqual(mockUser);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('Environment-based Token Routing', () => {
    it('should use Supabase JWT when SUPABASE_URL is configured', async () => {
      process.env.SUPABASE_URL = 'https://example.supabase.co';
      req.headers.authorization = 'Bearer token';
      
      await authenticateSupabaseUser(req, res, next);

      expect(verifySupabaseJWT).toHaveBeenCalledWith('token');
    });

    it('should use custom JWT when SUPABASE_URL contains placeholder', async () => {
      process.env.SUPABASE_URL = 'https://placeholder.supabase.co';
      req.headers.authorization = 'Bearer token';
      
      const { verifyAccessToken } = await import('../utils/tokens');
      vi.mocked(verifyAccessToken).mockReturnValueOnce({
        id: 'user-123',
        email: '<EMAIL>',
      });

      await authenticateSupabaseUser(req, res, next);

      expect(verifyAccessToken).toHaveBeenCalledWith('token');
    });
  });
});

// Additional comprehensive tests using supertest for full integration testing
describe('Auth Middleware Integration Tests', () => {
  const mockUser = { id: 'user-123', email: '<EMAIL>', name: 'Test User' };
  const mockToken = 'mock-jwt-token';
  let originalSupabaseUrl: string | undefined;
  let app: express.Application;

  beforeEach(() => {
    vi.resetAllMocks();
    originalSupabaseUrl = process.env.SUPABASE_URL;
    
    // Setup test express app
    app = express();
    app.use(authenticateSupabaseUser);
    app.get('/test', (req: Request, res: Response) => {
      const user = (req as SupabaseAuthenticatedRequest).user;
      res.status(HTTP_STATUS.OK).json({ user });
    });
    app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({ 
        message: 'Caught by error handler', 
        error: err.message 
      });
    });
  });

  afterEach(() => {
    process.env.SUPABASE_URL = originalSupabaseUrl;
  });

  describe('Error Response Patterns', () => {
    it('should return specific JWT error messages in 401 responses', async () => {
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      const errorMessage = 'Token has expired. Please login again.';
      vi.mocked(verifySupabaseJWT).mockRejectedValue(new SupabaseJWTError(errorMessage));

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`);

      expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
      expect(response.body.message).toBe(errorMessage);
    });

    it('should return generic message for unexpected errors', async () => {
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      vi.mocked(verifySupabaseJWT).mockRejectedValue(new Error('Connection timeout'));

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`);

      expect(response.status).toBe(HTTP_STATUS.INTERNAL_SERVER_ERROR);
      expect(response.body.error).toBe('Authentication error');
      expect(response.body.message).toBe('An error occurred during authentication');
    });
  });

  describe('Token Edge Cases', () => {
    it('should handle empty Bearer token', async () => {
      const response = await request(app)
        .get('/test')
        .set('Authorization', 'Bearer ');

      expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
      expect(response.body.message).toBe('Missing or invalid authorization header');
    });

    it('should handle malformed Bearer token', async () => {
      const response = await request(app)
        .get('/test')
        .set('Authorization', 'Bearer');

      expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
      expect(response.body.message).toBe('Missing or invalid authorization header');
    });

    it('should reject case-sensitive Bearer prefix', async () => {
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      vi.mocked(verifySupabaseJWT).mockResolvedValue(mockUser);

      const response = await request(app)
        .get('/test')
        .set('Authorization', `bearer ${mockToken}`);

      // extractBearerToken is case-sensitive and requires 'Bearer' with capital B
      expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
      expect(response.body.message).toBe('Missing or invalid authorization header');
      expect(verifySupabaseJWT).not.toHaveBeenCalled();
    });
  });

  describe('Concurrent Request Handling', () => {
    it('should handle multiple concurrent authentication requests', async () => {
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      vi.mocked(verifySupabaseJWT).mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return mockUser;
      });

      const requests = Array(5).fill(null).map(() =>
        request(app)
          .get('/test')
          .set('Authorization', `Bearer ${mockToken}`)
      );

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.body.user).toEqual(mockUser);
      });

      expect(verifySupabaseJWT).toHaveBeenCalledTimes(5);
    });
  });

  describe('Rate Limiting Resilience', () => {
    it('should not retry JWT verification on failure', async () => {
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      vi.mocked(verifySupabaseJWT).mockRejectedValue(new SupabaseJWTError('Invalid token'));

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`);

      expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
      expect(verifySupabaseJWT).toHaveBeenCalledTimes(1); // Only one attempt
    });

    it('should handle rate limit errors from Supabase', async () => {
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      const rateLimitError = new Error('Too many requests');
      (rateLimitError as any).status = 429;
      vi.mocked(verifySupabaseJWT).mockRejectedValue(rateLimitError);

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`);

      expect(response.status).toBe(HTTP_STATUS.INTERNAL_SERVER_ERROR);
      expect(verifySupabaseJWT).toHaveBeenCalledTimes(1);
    });
  });

  describe('Performance and Timeout Handling', () => {
    it('should handle verification timeouts gracefully', async () => {
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      vi.mocked(verifySupabaseJWT).mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`);

      expect(response.status).toBe(HTTP_STATUS.INTERNAL_SERVER_ERROR);
      expect(response.body.error).toBe('Authentication error');
    });
  });
});