// Load environment variables with proper precedence
import { loadEnvironment } from './utils/env-loader';
loadEnvironment();

// Validate environment variables before starting the server
import { validateEnvironmentVariables } from './utils/env-validation';
import { logger } from './utils/logger';

try {
  validateEnvironmentVariables();
} catch (error) {
  logger.error('Environment validation failed:', { error });
  process.exit(1);
}

import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { createSuccessResponse } from '@travelviz/shared';
import authRoutes from './routes/supabase-auth.routes';
import tripsRoutes from './routes/trips.routes';
import activitiesRoutes from './routes/activities.routes';
import testRoutes from './routes/test.routes';
import importRoutes from './routes/import.routes';
import modelsRoutes from './routes/models.routes';
// import affiliateRoutes from './routes/affiliate.routes'; // Disabled - not configured yet
import publicRoutes from './routes/public.routes';
import { placesRoutes } from './routes/places.routes';
import healthRoutes from './routes/health.routes';
import searchRoutes from './routes/search.routes';
import { monitoringRouter } from './api/monitoring.api';
import { errorHandler, notFoundHandler } from './middleware/error-handler.middleware';
import { requestIdMiddleware } from './middleware/request-id.middleware';
import { handlePayloadTooLarge } from './middleware/request-size.middleware';
import { performanceMiddleware } from './middleware/performance.middleware';

const app: Application = express();
const PORT = process.env.PORT || 3001;

// Trust proxy for Render deployment
// Note: Rate limiting uses secure key generation to prevent X-Forwarded-For spoofing
// See rate-limit.middleware.ts for implementation details
app.set('trust proxy', true);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:3000',
    'https://travelviz-nine.vercel.app'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
}));

// Request ID middleware (should be early in the chain)
app.use(requestIdMiddleware);

// Performance tracking middleware
app.use(performanceMiddleware());

// Request logging with custom format
app.use(morgan((tokens, req, res) => {
  const responseTime = tokens['response-time'](req, res);
  logger.logRequest(req, res, parseFloat(responseTime || '0'));
  return [
    tokens.method(req, res),
    tokens.url(req, res),
    tokens.status(req, res),
    responseTime ? `${responseTime}ms` : '-',
    req.id || '-'
  ].join(' ');
}));

// Payload too large handler MUST come BEFORE body parsing
app.use(handlePayloadTooLarge);

// Body parsing with size limits
// Note: Auth routes will apply their own stricter limits via middleware
app.use(express.json({ limit: '1mb' })); 
app.use(express.urlencoded({ extended: true, limit: '1mb' }));

// Custom security headers
app.use((_req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  next();
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json(createSuccessResponse({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'travelviz-hub',
  }));
});


// API v1 routes
app.use('/api/v1/health', healthRoutes); // Health check routes (no auth required)
app.use('/api/v1/public', publicRoutes); // Public routes (no auth required)
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/trips', tripsRoutes);
app.use('/api/v1/activities', activitiesRoutes);
app.use('/api/v1/test', testRoutes);
app.use('/api/v1/import', importRoutes);
app.use('/api/v1/models', modelsRoutes);
app.use('/api/v1/places', placesRoutes);
app.use('/api/v1/search', searchRoutes);
// app.use('/api/v1/affiliate', affiliateRoutes); // Disabled - not configured yet
app.use('/api/v1/monitoring', monitoringRouter);

// 404 handler
app.use('*', notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

// Start server only if not in test environment
if (process.env.NODE_ENV !== 'test') {
  const server = app.listen(PORT, () => {
    const actualPort = (server.address() as { port: number })?.port || PORT;
    logger.info(`🚀 TravelViz Hub API server running on port ${actualPort}`);
    logger.info(`📍 Health check: http://localhost:${actualPort}/health`);
    logger.info(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.info(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
  });
}

export default app; 