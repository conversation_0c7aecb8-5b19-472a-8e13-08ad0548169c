import { Request, Response, NextFunction } from 'express';
import { createErrorResponse, HTTP_STATUS } from '@travelviz/shared';
import { verifyAccessToken, JWTUser, TokenError } from '../utils/tokens';

export interface AuthenticatedRequest extends Request {
  user?: JWTUser;
}

/**
 * Middleware to verify JWT access token
 */
export const authenticateUser = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        createErrorResponse('Unauthorized', 'Missing or invalid authorization header')
      );
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!token) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        createErrorResponse('Unauthorized', 'JWT token is required')
      );
      return;
    }

    try {
      // Verify token and get user data
      const user = verifyAccessToken(token);
      req.user = user;
      next();
    } catch (error) {
      if (error instanceof TokenError) {
        res.status(error.statusCode).json(
          createErrorResponse('Unauthorized', error.message)
        );
        return;
      }
      
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        createErrorResponse('Unauthorized', 'Invalid or expired token')
      );
      return;
    }
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Authentication error', 'An error occurred during authentication')
    );
  }
};

/**
 * Optional authentication middleware
 * Adds user to request if valid token is provided, but doesn't fail if missing
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    // No token provided, continue without user data
    return next();
  }

  const token = authHeader.substring(7);
  
  if (!token) {
    return next();
  }

  try {
    const user = verifyAccessToken(token);
    req.user = user;
  } catch (error) {
    // Token invalid but continue anyway for optional auth
  }

  next();
};