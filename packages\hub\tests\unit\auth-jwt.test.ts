import { describe, it, expect, vi } from 'vitest';
import request from 'supertest';
import { generateTokenPair, verifyAccessToken } from '../utils/tokens';

// Mock the app before importing to prevent it from listening
vi.mock('../index', async () => {
  const actual = await vi.importActual<typeof import('../index')>('../index');
  return {
    ...actual,
    app: actual.default, // Export the app without starting the server
  };
});

// Import app after mocking
import { default as app } from '../index';

describe('JWT Authentication', () => {
  
  it('should use JWT_SECRET from environment variables', () => {
    // Ensure JWT_SECRET is set from environment
    expect(process.env.JWT_SECRET).toBeDefined();
    expect(process.env.JWT_SECRET).not.toBe('');
  });
  
  it('should generate valid token pair', () => {
    const user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    };
    
    const tokens = generateTokenPair(user);
    
    expect(tokens.accessToken).toBeDefined();
    expect(tokens.refreshToken).toBeDefined();
    expect(tokens.expiresIn).toBeGreaterThan(0);
  });
  
  it('should verify access token correctly', () => {
    const user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    };
    
    const tokens = generateTokenPair(user);
    const verified = verifyAccessToken(tokens.accessToken);
    
    expect(verified.id).toBe(user.id);
    expect(verified.email).toBe(user.email);
    expect(verified.name).toBe(user.name);
  });
  
  it('should reject invalid tokens', () => {
    expect(() => {
      verifyAccessToken('invalid-token');
    }).toThrow();
  });
  
  it('should protect routes with auth middleware', async () => {
    const response = await request(app)
      .get('/api/v1/trips')
      .expect(401);
    
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toBe('Unauthorized');
  });
});