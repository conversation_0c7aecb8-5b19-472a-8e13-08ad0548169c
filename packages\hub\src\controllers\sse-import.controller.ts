import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth.middleware';
import { getAIParserService } from '../services/ai-parser.service';
import { redis, supportsPubSub } from '../config/redis';
import { cacheService } from '../services/cache.service';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';
import { logger } from '../utils/logger';

/**
 * Enhanced SSE Import Controller with Redis pub/sub
 * Provides real-time progress updates for AI conversation imports
 */
export class SSEImportController {
  private readonly progressChannel = 'parse:progress:';
  private readonly heartbeatInterval = 30000; // 30 seconds

  /**
   * Start a new import session
   */
  async parseText(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { text, source = 'unknown' } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json(createErrorResponse('Unauthorized'));
        return;
      }

      if (!text || typeof text !== 'string') {
        res.status(400).json(createErrorResponse('Invalid text content'));
        return;
      }

      // Create parse session via AI parser service
      const sessionId = await getAIParserService().createParseSession(text, source, userId);

      logger.info('Started import session', { sessionId, userId, source });

      // Return session ID for SSE connection
      res.status(200).json(createSuccessResponse({
        sessionId,
        sseUrl: `/api/import/progress/${sessionId}`,
        estimatedTime: 15 // seconds
      }));
    } catch (error) {
      logger.error('Failed to start import', { error });
      res.status(500).json(createErrorResponse(
        error instanceof Error ? error.message : 'Failed to start import'
      ));
    }
  }

  /**
   * Stream progress updates via Server-Sent Events
   */
  async streamProgress(req: AuthenticatedRequest, res: Response): Promise<void> {
    const { sessionId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json(createErrorResponse('Unauthorized'));
      return;
    }

    // Verify session exists and belongs to user
    const session = await getAIParserService().getSession(sessionId);
    if (!session) {
      res.status(404).json(createErrorResponse('Import session not found'));
      return;
    }

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'X-Accel-Buffering': 'no', // Disable Nginx buffering
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || 'http://localhost:3000',
      'Access-Control-Allow-Credentials': 'true'
    });

    // Send immediate first event to establish connection
    res.write(':ok\n\n');

    // Get initial progress from cache if available
    const cachedProgress = await cacheService.get(`progress:${sessionId}`);
    if (cachedProgress) {
      res.write(`data: ${JSON.stringify({
        type: 'progress',
        ...cachedProgress
      })}\n\n`);
    }

    // Set up Redis subscription
    const channel = `${this.progressChannel}${sessionId}`;
    
    // Subscribe to progress updates (if supported)
    // Note: Pub/sub is only available in test environment as Upstash Redis doesn't support it
    if (supportsPubSub(redis)) {
      await redis.subscribe(channel);

      // Handle Redis messages
      redis.on('message', (receivedChannel: string, message: string) => {
      if (receivedChannel === channel) {
        try {
          const update = JSON.parse(message);
          res.write(`data: ${JSON.stringify({
            type: 'progress',
            ...update
          })}\n\n`);

          // Check if complete
          if (update.progress === 100 || update.step === 'complete') {
            // Send completion event
            res.write(`data: ${JSON.stringify({
              type: 'complete',
              sessionId
            })}\n\n`);
            
            // Clean up
            if (supportsPubSub(redis)) {
              redis.unsubscribe(channel);
            }
            res.end();
          } else if (update.step === 'error') {
            // Send error event
            res.write(`data: ${JSON.stringify({
              type: 'error',
              message: update.message
            })}\n\n`);
            
            // Clean up
            if (supportsPubSub(redis)) {
              redis.unsubscribe(channel);
            }
            res.end();
          }
        } catch (error) {
          logger.error('Failed to process Redis message', { error, message });
        }
      }
    });
    }

    // Set up heartbeat to keep connection alive
    const heartbeat = setInterval(() => {
      res.write(':heartbeat\n\n');
    }, this.heartbeatInterval);

    // Clean up on client disconnect
    req.on('close', () => {
      clearInterval(heartbeat);
      if (supportsPubSub(redis)) {
        redis.unsubscribe(channel);
      }
      logger.debug('SSE client disconnected', { sessionId });
    });

    // Clean up on request abort
    req.on('aborted', () => {
      clearInterval(heartbeat);
      if (supportsPubSub(redis)) {
        redis.unsubscribe(channel);
      }
      logger.debug('SSE request aborted', { sessionId });
    });
  }

  /**
   * Get import session status
   */
  async getSessionStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json(createErrorResponse('Unauthorized'));
        return;
      }

      const session = await getAIParserService().getSession(sessionId);
      if (!session) {
        res.status(404).json(createErrorResponse('Session not found'));
        return;
      }

      res.status(200).json(createSuccessResponse({
        sessionId: session.id,
        status: session.status,
        progress: session.progress,
        currentStep: session.currentStep,
        error: session.error,
        startedAt: session.startedAt,
        completedAt: session.completedAt
      }));
    } catch (error) {
      logger.error('Failed to get session status', { error });
      res.status(500).json(createErrorResponse(
        error instanceof Error ? error.message : 'Failed to get status'
      ));
    }
  }

  /**
   * Create trip from completed import
   */
  async createTripFromImport(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const { edits } = req.body; // Optional edits to parsed data
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json(createErrorResponse('Unauthorized'));
        return;
      }

      // Create trip from parsed data
      const tripId = await getAIParserService().createTripFromParse(sessionId, userId, edits);

      logger.info('Created trip from import', { sessionId, tripId, userId });

      res.status(201).json(createSuccessResponse({
        tripId,
        redirectUrl: `/trips/${tripId}`
      }));
    } catch (error) {
      logger.error('Failed to create trip from import', { error });
      res.status(500).json(createErrorResponse(
        error instanceof Error ? error.message : 'Failed to create trip'
      ));
    }
  }
}