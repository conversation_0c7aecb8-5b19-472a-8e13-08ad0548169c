import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { api } from '@/lib/api-client';
import { cookieStorage } from '@/lib/cookie-storage';
import type { User } from '@travelviz/shared';

export interface AuthState {
  // State
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setUser: (user: User | null) => void;
  setTokens: (accessToken: string | null, refreshToken: string | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, name?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  clearAuth: () => void;
}


export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      setUser: (user) => 
        set({ 
          user, 
          isAuthenticated: !!user,
          error: null 
        }),

      setTokens: (accessToken, refreshToken) => 
        set({ 
          accessToken, 
          refreshToken,
          isAuthenticated: !!accessToken 
        }),

      setLoading: (isLoading) => 
        set({ isLoading }),

      setError: (error) => 
        set({ error }),

      login: async (email, password) => {
        set({ isLoading: true, error: null });
        
        try {
          const data = await api.auth.login(email, password);
          const { user, access_token, refresh_token } = data;
          
          set({
            user: {
              id: user.id,
              email: user.email,
              name: user.name || undefined,
              avatar_url: user.avatar_url || undefined,
            },
            accessToken: access_token,
            refreshToken: refresh_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      signup: async (email, password, name) => {
        set({ isLoading: true, error: null });
        
        try {
          const data = await api.auth.signup(email, password, name);
          const { user, access_token, refresh_token } = data;
          
          set({
            user: {
              id: user.id,
              email: user.email,
              name: user.name || name || undefined,
              avatar_url: user.avatar_url || undefined,
            },
            accessToken: access_token,
            refreshToken: refresh_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Signup failed',
          });
          throw error;
        }
      },

      logout: async () => {
        const { accessToken } = get();
        
        set({ isLoading: true });
        
        try {
          if (accessToken) {
            await api.auth.logout();
          }
        } catch (error) {
          // Ignore logout errors
          // Ignore logout errors - clearing auth state anyway
        } finally {
          get().clearAuth();
        }
      },

      refreshSession: async () => {
        const { refreshToken } = get();
        
        if (!refreshToken) {
          get().clearAuth();
          throw new Error('No refresh token');
        }

        try {
          const data = await api.auth.refresh(refreshToken);
          const { user, access_token, refresh_token } = data;
          
          set({
            user: {
              id: user.id,
              email: user.email,
              name: user.name || undefined,
              avatar_url: user.avatar_url || undefined,
            },
            accessToken: access_token,
            refreshToken: refresh_token,
            isAuthenticated: true,
            error: null,
          });
        } catch (error) {
          get().clearAuth();
          throw error;
        }
      },

      clearAuth: () => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => cookieStorage),
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);