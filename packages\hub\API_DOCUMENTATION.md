# TravelViz Hub API Documentation

## Table of Contents

- [Overview](#overview)
- [Authentication](#authentication)
- [Rate Limiting](#rate-limiting)
- [Response Format](#response-format)
- [Error Codes](#error-codes)
- [API Endpoints](#api-endpoints)
  - [Search Routes](#search-routes)
  - [Authentication Routes](#authentication-routes)
  - [Trip Management Routes](#trip-management-routes)
  - [Activity Management Routes](#activity-management-routes)
  - [Import Routes](#import-routes)
  - [Places Integration Routes](#places-integration-routes)
  - [Public Access Routes](#public-access-routes)
  - [Affiliate Services Routes](#affiliate-services-routes)
  - [Health & Monitoring Routes](#health--monitoring-routes)
  - [Model Configuration Routes](#model-configuration-routes)
  - [Testing Routes](#testing-routes)

## Overview

The TravelViz Hub API is a RESTful API that serves as the central backend for the TravelViz travel planning application. All endpoints are prefixed with `/api/v1`.

**Base URL**: `http://localhost:3001/api/v1` (development)

## Authentication

Most endpoints require authentication via Supabase JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

Public endpoints that don't require authentication are clearly marked.

## Rate Limiting

Different endpoints have different rate limits:

- **Standard API**: Default rate limits
- **Authentication**: 5 requests per 15 minutes
- **Login**: 10 requests per 15 minutes
- **Password Reset**: Strict limits to prevent enumeration
- **Import Operations**: Special limits for heavy operations
- **Places API**: 30 requests per minute (cost control)

## Response Format

All responses follow a consistent format:

### Success Response

```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response

```json
{
  "success": false,
  "error": "Error message",
  "details": "Additional error details",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing or invalid auth)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error
- `503` - Service Unavailable

## API Endpoints

### Search Routes

#### GET /api/v1/search/trips

Search trips by query using full-text search.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Uses DOMPurify for input sanitization

**Query Parameters**:

- `q` (required): Search query string (min 2 characters, max 100 characters)

**Response**:

```json
{
  "success": true,
  "data": {
    "query": "paris",
    "results": [
      {
        "id": "uuid",
        "title": "Summer in Paris",
        "description": "Trip to Paris, France",
        "start_date": "2024-07-01",
        "end_date": "2024-07-15",
        "destinations": ["Paris"],
        "activities": [...]
      }
    ],
    "total": 5
  }
}
```

---

#### POST /api/v1/search/trips

Search trips with filters.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Uses DOMPurify for input sanitization

**Request Body**:

```json
{
  "query": "europe",
  "filters": {
    "status": "active",
    "startDate": "2024-07-01",
    "endDate": "2024-12-31"
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "query": "europe",
    "filters": {
      "status": "active",
      "startDate": "2024-07-01",
      "endDate": "2024-12-31"
    },
    "results": [...],
    "total": 10
  }
}
```

### Authentication Routes

#### POST /api/v1/auth/signup

Create a new user account.

**Authentication**: Not required  
**Rate Limit**: 5 requests per 15 minutes  
**Content Limit**: Configured by SIZE_LIMITS.AUTH

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "name": "John Doe" // optional
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "created_at": "2024-01-01T00:00:00.000Z"
    },
    "session": {
      "access_token": "jwt_token",
      "refresh_token": "refresh_token",
      "expires_in": 3600
    }
  }
}
```

---

#### POST /api/v1/auth/login

Authenticate an existing user.

**Authentication**: Not required  
**Rate Limit**: 10 requests per 15 minutes  
**Content Limit**: Configured by SIZE_LIMITS.AUTH

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "session": {
      "access_token": "jwt_token",
      "refresh_token": "refresh_token",
      "expires_in": 3600
    }
  }
}
```

---

#### POST /api/v1/auth/refresh

Refresh an expired access token.

**Authentication**: Not required  
**Rate Limit**: 5 requests per 15 minutes  
**Content Limit**: Configured by SIZE_LIMITS.AUTH

**Request Body**:

```json
{
  "refresh_token": "your_refresh_token"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "session": {
      "access_token": "new_jwt_token",
      "refresh_token": "new_refresh_token",
      "expires_in": 3600
    }
  }
}
```

---

#### POST /api/v1/auth/reset-password

Request a password reset email.

**Authentication**: Not required  
**Rate Limit**: Strict rate limit to prevent enumeration  
**Content Limit**: Configured by SIZE_LIMITS.AUTH

**Request Body**:

```json
{
  "email": "<EMAIL>"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Password reset email sent if account exists"
  }
}
```

---

#### POST /api/v1/auth/reset-password/confirm

Confirm password reset with token.

**Authentication**: Not required  
**Rate Limit**: 5 requests per 15 minutes  
**Content Limit**: Configured by SIZE_LIMITS.AUTH

**Request Body**:

```json
{
  "token": "reset_token_from_email",
  "password": "NewSecurePassword123!"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Password successfully reset"
  }
}
```

---

#### POST /api/v1/auth/logout

Log out the current user.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Successfully logged out"
  }
}
```

---

#### GET /api/v1/auth/me

Get current user information.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Response**:

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "John Doe",
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

---

#### POST /api/v1/auth/change-password

Change the current user's password.

**Authentication**: Required  
**Rate Limit**: 5 requests per 15 minutes  
**Content Limit**: Configured by SIZE_LIMITS.AUTH

**Request Body**:

```json
{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewSecurePassword123!"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Password successfully changed"
  }
}
```

### Trip Management Routes

#### POST /api/v1/trips

Create a new trip.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates createTripSchema, verifies ownership, invalidates user-trips cache

**Request Body**:

```json
{
  "title": "Summer Vacation 2024",
  "description": "Family trip to Europe",
  "startDate": "2024-07-01",
  "endDate": "2024-07-15",
  "destinations": ["Paris", "Rome", "Barcelona"],
  "travelers": 4,
  "budget": {
    "amount": 5000,
    "currency": "USD"
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "trip": {
      "id": "uuid",
      "title": "Summer Vacation 2024",
      "description": "Family trip to Europe",
      "startDate": "2024-07-01",
      "endDate": "2024-07-15",
      "destinations": ["Paris", "Rome", "Barcelona"],
      "travelers": 4,
      "budget": {
        "amount": 5000,
        "currency": "USD"
      },
      "userId": "user_uuid",
      "isPublic": false,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

---

#### GET /api/v1/trips

Get all trips for the authenticated user.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Filters owned resources, caches user trips

**Query Parameters**:

- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `sort` (optional): Sort field (createdAt, updatedAt, startDate)
- `order` (optional): Sort order (asc, desc)

**Response**:

```json
{
  "success": true,
  "data": {
    "trips": [
      {
        "id": "uuid",
        "title": "Summer Vacation 2024",
        "description": "Family trip to Europe",
        "startDate": "2024-07-01",
        "endDate": "2024-07-15",
        "destinations": ["Paris", "Rome", "Barcelona"],
        "activityCount": 12,
        "isPublic": false,
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

---

#### GET /api/v1/trips/:id

Get a specific trip by ID.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates trip ID, verifies ownership, caches trip details

**Response**:

```json
{
  "success": true,
  "data": {
    "trip": {
      "id": "uuid",
      "title": "Summer Vacation 2024",
      "description": "Family trip to Europe",
      "startDate": "2024-07-01",
      "endDate": "2024-07-15",
      "destinations": ["Paris", "Rome", "Barcelona"],
      "travelers": 4,
      "budget": {
        "amount": 5000,
        "currency": "USD"
      },
      "activities": [
        {
          "id": "activity_uuid",
          "name": "Eiffel Tower Visit",
          "date": "2024-07-02",
          "time": "10:00",
          "duration": 120,
          "location": { ... }
        }
      ],
      "isPublic": false,
      "shareSlug": null,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

---

#### PUT /api/v1/trips/:id

Update a trip.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates trip ID and update schema, verifies ownership, invalidates caches

**Request Body**:

```json
{
  "title": "Updated Summer Vacation 2024",
  "description": "Extended family trip to Europe",
  "endDate": "2024-07-20",
  "budget": {
    "amount": 6000,
    "currency": "USD"
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "trip": {
      "id": "uuid",
      "title": "Updated Summer Vacation 2024"
      // ... updated trip data
    }
  }
}
```

---

#### DELETE /api/v1/trips/:id

Delete a trip.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates trip ID, verifies ownership, invalidates caches

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Trip successfully deleted"
  }
}
```

---

#### POST /api/v1/trips/:tripId/activities

Add an activity to a trip.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates activity schema, verifies trip ownership, invalidates trip cache

**Request Body**:

```json
{
  "name": "Louvre Museum Tour",
  "description": "Guided tour of the Louvre",
  "date": "2024-07-03",
  "time": "14:00",
  "duration": 180,
  "location": {
    "name": "Louvre Museum",
    "address": "Rue de Rivoli, 75001 Paris, France",
    "coordinates": {
      "lat": 48.8606,
      "lng": 2.3376
    }
  },
  "price": {
    "amount": 17,
    "currency": "EUR"
  },
  "category": "sightseeing",
  "bookingUrl": "https://www.louvre.fr/en/tickets"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "activity": {
      "id": "activity_uuid",
      "tripId": "trip_uuid",
      "name": "Louvre Museum Tour"
      // ... full activity data
    }
  }
}
```

---

#### PATCH /api/v1/trips/:tripId/activities/reorder

Reorder activities within a trip.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates reorder schema, verifies trip ownership, invalidates trip cache

**Request Body**:

```json
{
  "activityIds": ["activity_uuid_3", "activity_uuid_1", "activity_uuid_2"]
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Activities reordered successfully",
    "activities": [
      // ... reordered activities
    ]
  }
}
```

---

#### POST /api/v1/trips/:id/clone

Clone an existing trip.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates trip ID, verifies ownership

**Request Body** (optional):

```json
{
  "title": "Copy of Summer Vacation 2024",
  "startDate": "2025-07-01",
  "endDate": "2025-07-15"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "trip": {
      "id": "new_uuid",
      "title": "Copy of Summer Vacation 2024"
      // ... cloned trip data
    }
  }
}
```

---

#### POST /api/v1/trips/:id/share

Make a trip publicly shareable.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates trip ID, verifies ownership

**Response**:

```json
{
  "success": true,
  "data": {
    "slug": "summer-europe-2024-abc123",
    "shareUrl": "https://travelviz.ai/p/summer-europe-2024-abc123"
  }
}
```

---

#### DELETE /api/v1/trips/:id/share

Make a trip private (remove public access).

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates trip ID, verifies ownership

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Trip is now private"
  }
}
```

### Activity Management Routes

#### POST /api/v1/activities/add-from-place

Add an activity from a Google Place.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Request Body**:

```json
{
  "tripId": "trip_uuid",
  "placeId": "ChIJD7fiBh9u5kcRYJSMaMOCCwQ",
  "date": "2024-07-03",
  "time": "14:00",
  "duration": 120,
  "notes": "Don't forget to book tickets in advance"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "activity": {
      "id": "activity_uuid",
      "tripId": "trip_uuid",
      "name": "Louvre Museum",
      "location": {
        "name": "Louvre Museum",
        "address": "Rue de Rivoli, 75001 Paris, France",
        "coordinates": {
          "lat": 48.8606,
          "lng": 2.3376
        },
        "placeId": "ChIJD7fiBh9u5kcRYJSMaMOCCwQ"
      }
      // ... rest of activity data
    }
  }
}
```

---

#### PUT /api/v1/activities/:id

Update an activity.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates activity ID and update schema, verifies activity ownership

**Request Body**:

```json
{
  "name": "Updated Louvre Museum Tour",
  "time": "15:00",
  "duration": 240,
  "notes": "Extended tour with audio guide",
  "price": {
    "amount": 25,
    "currency": "EUR"
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "activity": {
      "id": "activity_uuid"
      // ... updated activity data
    }
  }
}
```

---

#### DELETE /api/v1/activities/:id

Delete an activity.

**Authentication**: Required  
**Rate Limit**: Standard API limits  
**Middleware**: Validates activity ID, verifies activity ownership

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Activity successfully deleted"
  }
}
```

### Import Routes

#### POST /api/v1/import/parse-direct

Parse and create a trip from AI conversation text (synchronous).

**Authentication**: Required  
**Rate Limit**: Import rate limit  
**Content Limit**: 50KB (50,000 characters)  
**Middleware**: Validates parse schema, validates text content

**Request Body**:

```json
{
  "content": "User: I want to plan a trip to Paris...\nAI: Great! Let me help you...",
  "source": "chatgpt"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "trip": {
      "id": "trip_uuid",
      "title": "Paris Adventure"
      // ... full trip data with activities
    }
  }
}
```

---

#### POST /api/v1/import/parse

Parse AI conversation with Server-Sent Events progress updates.

**Authentication**: Required  
**Rate Limit**: Import rate limit  
**Content Limit**: 50KB (50,000 characters)  
**Middleware**: Validates parse schema, validates text content

**Request Body**:

```json
{
  "content": "User: I want to plan a trip to Paris...\nAI: Great! Let me help you...",
  "source": "claude"
}
```

**Response**: Server-Sent Events stream

```
data: {"type":"start","sessionId":"session_uuid","message":"Starting parsing..."}

data: {"type":"progress","progress":25,"message":"Extracting trip details..."}

data: {"type":"progress","progress":50,"message":"Parsing activities..."}

data: {"type":"complete","progress":100,"result":{...},"message":"Parsing complete"}
```

---

#### POST /api/v1/import/upload

Upload and parse a PDF file.

**Authentication**: Required  
**Rate Limit**: Import rate limit  
**File Limit**: 10MB, PDF only  
**Middleware**: Validates upload schema, validates file upload

**Request**: Multipart form data

- `file`: PDF file
- `source`: Source identifier (e.g., "pdf")

**Response**:

```json
{
  "success": true,
  "data": {
    "sessionId": "session_uuid",
    "message": "PDF uploaded and parsing started"
  }
}
```

---

#### GET /api/v1/import/progress/:sessionId

Get Server-Sent Events stream for import progress.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Response**: Server-Sent Events stream (same format as POST /parse)

---

#### GET /api/v1/import/status/:sessionId

Get import session status (non-streaming).

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Response**:

```json
{
  "success": true,
  "data": {
    "sessionId": "session_uuid",
    "status": "completed",
    "progress": 100,
    "result": {
      "tripData": { ... },
      "activities": [ ... ]
    }
  }
}
```

---

#### POST /api/v1/import/:sessionId/create-trip

Create a trip from a completed import session.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Request Body** (optional):

```json
{
  "edits": {
    "title": "Modified Trip Title",
    "startDate": "2024-08-01"
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "trip": {
      "id": "trip_uuid"
      // ... created trip data
    }
  }
}
```

---

#### POST /api/v1/import/parse-simple

Simple AI parsing without SSE (MVP endpoint).

**Authentication**: Required  
**Rate Limit**: Import rate limit  
**Content Limit**: 50KB

**Request Body**:

```json
{
  "content": "User: I want to plan a trip to Paris...",
  "source": "gemini"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "importId": "import_uuid",
    "message": "Parsing started"
  }
}
```

---

#### GET /api/v1/import/parse-simple/:importId

Get simple parse session status.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Response**:

```json
{
  "success": true,
  "data": {
    "importId": "import_uuid",
    "status": "processing",
    "progress": 75,
    "currentStep": "Extracting activities",
    "result": null,
    "error": null,
    "startedAt": "2024-01-01T00:00:00.000Z",
    "completedAt": null
  }
}
```

---

#### POST /api/v1/import/parse-simple/:importId/create-trip

Create trip from simple parse session.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Request Body** (optional):

```json
{
  "edits": {
    "title": "My Paris Trip",
    "budget": {
      "amount": 3000,
      "currency": "EUR"
    }
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "tripId": "trip_uuid",
    "message": "Trip created successfully"
  }
}
```

---

#### POST /api/v1/import/pdf

Import PDF with SSE support.

**Authentication**: Required  
**Rate Limit**: Import rate limit  
**File Limit**: 10MB, PDF only

**Request**: Multipart form data

- `file`: PDF file

**Response**:

```json
{
  "success": true,
  "data": {
    "sessionId": "session_uuid",
    "sseUrl": "/api/v1/import/progress/session_uuid",
    "source": "pdf",
    "metadata": {
      "filename": "travel-itinerary.pdf",
      "pageCount": 5,
      "size": 1048576
    },
    "estimatedTime": 30
  }
}
```

### Places Integration Routes

#### GET /api/v1/places/autocomplete

Search for places using Google Places Autocomplete.

**Authentication**: Required  
**Rate Limit**: 30 requests per minute (cost control)

**Query Parameters**:

- `query` (required): Search query string (min 1 character)

**Response**:

```json
{
  "success": true,
  "data": {
    "predictions": [
      {
        "description": "Eiffel Tower, Avenue Anatole France, Paris, France",
        "place_id": "ChIJLU7jZClu5kcR4PcOOO6p3I0",
        "structured_formatting": {
          "main_text": "Eiffel Tower",
          "secondary_text": "Avenue Anatole France, Paris, France"
        },
        "types": ["tourist_attraction", "point_of_interest", "establishment"]
      }
    ],
    "status": "OK"
  }
}
```

---

#### GET /api/v1/places/:placeId

Get detailed information about a specific place.

**Authentication**: Required  
**Rate Limit**: 30 requests per minute (cost control)

**Response**:

```json
{
  "success": true,
  "data": {
    "result": {
      "place_id": "ChIJLU7jZClu5kcR4PcOOO6p3I0",
      "name": "Eiffel Tower",
      "formatted_address": "Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France",
      "geometry": {
        "location": {
          "lat": 48.8584,
          "lng": 2.2945
        }
      },
      "types": ["tourist_attraction", "point_of_interest", "establishment"],
      "rating": 4.6,
      "user_ratings_total": 250000,
      "opening_hours": {
        "weekday_text": [
          "Monday: 9:30 AM – 11:45 PM"
          // ... other days
        ]
      },
      "website": "https://www.toureiffel.paris/",
      "formatted_phone_number": "+33 892 70 12 39",
      "photos": [
        {
          "photo_reference": "photo_reference_string",
          "height": 3024,
          "width": 4032
        }
      ]
    },
    "status": "OK"
  }
}
```

### Public Access Routes

#### GET /api/v1/public/trips/:slug

Get a publicly shared trip.

**Authentication**: Not required  
**Rate Limit**: Standard API limits  
**Cache**: 1 hour TTL

**Response**:

```json
{
  "success": true,
  "data": {
    "trip": {
      "id": "uuid",
      "title": "Summer Vacation 2024",
      "description": "Family trip to Europe",
      "startDate": "2024-07-01",
      "endDate": "2024-07-15",
      "destinations": ["Paris", "Rome", "Barcelona"],
      "activities": [
        // ... public activity data
      ]
    },
    "activities": [
      // ... detailed activity list
    ],
    "viewCount": 42
  }
}
```

---

#### GET /api/v1/public/trips/:slug/preview

Get minimal trip data for social media sharing.

**Authentication**: Not required  
**Rate Limit**: Standard API limits  
**Cache**: Separate cache with 1 hour TTL

**Response**:

```json
{
  "success": true,
  "data": {
    "title": "Summer Vacation 2024",
    "description": "Family trip to Europe",
    "destinations": ["Paris", "Rome", "Barcelona"],
    "duration": "15 days",
    "activityCount": 24,
    "imageUrl": "https://example.com/trip-preview.jpg"
  }
}
```

---

#### GET /api/v1/public/check-slug/:slug

Check if a slug is available for use.

**Authentication**: Not required  
**Rate Limit**: Standard API limits  
**Cache**: 1 minute TTL

**Response**:

```json
{
  "success": true,
  "data": {
    "available": true,
    "slug": "summer-europe-2024"
  }
}
```

Or if unavailable:

```json
{
  "success": true,
  "data": {
    "available": false,
    "reason": "already_taken",
    "slug": "summer-europe-2024"
  }
}
```

### Affiliate Services Routes

#### POST /api/v1/affiliate/flights/search

Search for flights using Travelpayouts API.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Request Body**:

```json
{
  "origin": "CDG",
  "destination": "FCO",
  "departDate": "2024-07-05",
  "returnDate": "2024-07-12",
  "currency": "EUR",
  "adults": 2,
  "children": 0,
  "infants": 0,
  "tripClass": "Y"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "flights": [
      {
        "price": 234.50,
        "airline": "Air France",
        "duration": 135,
        "stops": 0,
        "departureTime": "2024-07-05T10:30:00",
        "arrivalTime": "2024-07-05T12:45:00",
        "bookingUrl": "https://affiliate-link.com/..."
      }
    ],
    "count": 15,
    "searchParams": { ... }
  }
}
```

---

#### POST /api/v1/affiliate/hotels/search

Search for hotels using Travelpayouts API.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Request Body**:

```json
{
  "location": "Paris, France",
  "checkIn": "2024-07-05",
  "checkOut": "2024-07-12",
  "currency": "EUR",
  "adults": 2,
  "children": [],
  "limit": 20
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "hotels": [
      {
        "name": "Hotel Example Paris",
        "stars": 4,
        "price": 125.00,
        "currency": "EUR",
        "location": {
          "lat": 48.8584,
          "lng": 2.2945
        },
        "amenities": ["wifi", "breakfast", "parking"],
        "rating": 8.5,
        "bookingUrl": "https://affiliate-link.com/..."
      }
    ],
    "count": 20,
    "searchParams": { ... }
  }
}
```

---

#### POST /api/v1/affiliate/track

Track affiliate link clicks.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Request Body**:

```json
{
  "activityId": "activity_uuid",
  "url": "https://booking.com/hotel-example",
  "affiliateType": "hotel"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "trackingId": "tracking_uuid",
    "affiliateUrl": "https://affiliate.travelpayouts.com/...",
    "redirect": true
  }
}
```

---

#### GET /api/v1/affiliate/airports/search

Search for airports (autocomplete).

**Authentication**: Not required  
**Rate Limit**: Standard API limits

**Query Parameters**:

- `q` (required): Search query (min 2 characters)

**Response**:

```json
{
  "success": true,
  "data": {
    "airports": [
      {
        "code": "CDG",
        "name": "Charles de Gaulle Airport",
        "city": "Paris",
        "country": "France",
        "coordinates": {
          "lat": 49.0097,
          "lng": 2.5479
        }
      }
    ],
    "count": 5
  }
}
```

---

#### GET /api/v1/affiliate/analytics

Get affiliate performance analytics.

**Authentication**: Required  
**Rate Limit**: Standard API limits

**Query Parameters**:

- `days` (optional): Number of days to look back (default: 30)

**Response**:

```json
{
  "success": true,
  "data": {
    "totalClicks": 156,
    "clicksByType": {
      "flight": 89,
      "hotel": 52,
      "car": 10,
      "activity": 5
    },
    "clicksByDay": {
      "2024-01-01": 12,
      "2024-01-02": 8
      // ... more days
    },
    "period": "30 days"
  }
}
```

### Health & Monitoring Routes

#### GET /api/v1/health

Main health check with comprehensive system status.

**Authentication**: Not required  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "environment": "production",
    "version": "1.0.0",
    "services": {
      "database": "healthy",
      "cache": "healthy",
      "connectionPool": "healthy"
    },
    "metrics": {
      "memory": {
        "used": 256,
        "total": 512,
        "unit": "MB"
      },
      "cache": {
        "redis": {
          "connected": true,
          "operations": 1234,
          "hitRate": 0.85
        },
        "multiLayer": {
          "hitRate": {
            "overall": 0.92,
            "memory": 0.65,
            "redis": 0.35
          }
        }
      },
      "connectionPool": {
        "activeConnections": 5,
        "idleConnections": 10,
        "totalRequests": 50000
      },
      "performance": {
        "operations": 25,
        "slowQueries": 2,
        "totalOperations": 150000
      }
    }
  }
}
```

---

#### GET /api/v1/health/live

Simple liveness probe for container orchestration.

**Authentication**: Not required  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "status": "ok"
  }
}
```

---

#### GET /api/v1/health/ready

Readiness probe to check if service can accept traffic.

**Authentication**: Not required  
**Rate Limit**: None

**Response** (when ready):

```json
{
  "success": true,
  "data": {
    "ready": true
  }
}
```

**Response** (when not ready):

```json
{
  "success": true,
  "data": {
    "ready": false,
    "reason": "Database not ready"
  }
}
```

---

#### GET /api/v1/health/monitoring/connections

Database connection pool monitoring.

**Authentication**: Not required  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "pool": {
      "stats": {
        "activeConnections": 5,
        "idleConnections": 10,
        "waitingRequests": 0,
        "totalRequests": 50000,
        "failedRequests": 12,
        "connectionErrors": 3,
        "avgRequestTime": 45.2
      },
      "health": {
        "healthy": true,
        "status": "optimal",
        "message": "Connection pool operating normally"
      },
      "thresholds": {
        "maxConnections": 50,
        "warningThreshold": 40,
        "criticalThreshold": 45
      }
    }
  }
}
```

---

#### GET /api/v1/health/monitoring/performance

Performance monitoring metrics.

**Authentication**: Not required  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "stats": [
      {
        "operation": "trips.list",
        "count": 15000,
        "avgDuration": 45.5,
        "minDuration": 12,
        "maxDuration": 230
      }
    ],
    "slowOperations": [
      {
        "operation": "import.parse",
        "duration": 5230,
        "timestamp": "2024-01-01T00:00:00.000Z"
      }
    ],
    "summary": {
      "totalOperations": 150000,
      "averageResponseTime": 52.3,
      "slowQueriesCount": 15
    }
  }
}
```

---

#### GET /api/v1/health/monitoring/cache

Cache system monitoring.

**Authentication**: Not required  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "redis": {
      "connected": true,
      "operations": 45000,
      "hitRate": 0.85,
      "memoryUsage": "125MB",
      "evictions": 230
    },
    "multiLayer": {
      "memory": {
        "entries": 150,
        "calculatedSize": 15728640,
        "hits": 8500,
        "misses": 1500
      },
      "redis": {
        "hits": 3500,
        "misses": 500
      },
      "hitRate": {
        "overall": 0.92,
        "memory": 0.65,
        "redis": 0.35
      }
    },
    "effectiveness": {
      "totalHitRate": 0.92,
      "memoryUtilization": 31.5
    }
  }
}
```

---

#### GET /api/v1/health/metrics

Prometheus-compatible metrics endpoint.

**Authentication**: Not required  
**Rate Limit**: None  
**Content-Type**: text/plain

**Response**:

```
# HELP travelviz_hub_uptime_seconds Server uptime in seconds
# TYPE travelviz_hub_uptime_seconds gauge
travelviz_hub_uptime_seconds 3600

# HELP travelviz_hub_memory_usage_bytes Memory usage in bytes
# TYPE travelviz_hub_memory_usage_bytes gauge
travelviz_hub_memory_usage_bytes{type="heapUsed"} *********
travelviz_hub_memory_usage_bytes{type="heapTotal"} *********
travelviz_hub_memory_usage_bytes{type="rss"} *********

# HELP travelviz_hub_connection_pool Database connection pool metrics
# TYPE travelviz_hub_connection_pool gauge
travelviz_hub_connection_pool{type="active"} 5
travelviz_hub_connection_pool{type="idle"} 10
travelviz_hub_connection_pool{type="total_requests"} 50000
travelviz_hub_connection_pool{type="failed_requests"} 12
travelviz_hub_connection_pool{type="errors"} 3

# HELP travelviz_hub_cache_hits Cache hit metrics
# TYPE travelviz_hub_cache_hits counter
travelviz_hub_cache_hits{layer="memory"} 8500
travelviz_hub_cache_hits{layer="redis"} 3500
travelviz_hub_cache_misses 2000

# HELP travelviz_hub_api_operations API operation metrics
# TYPE travelviz_hub_api_operations summary
travelviz_hub_api_operations_count{operation="trips.list"} 15000
travelviz_hub_api_operations_duration_ms{operation="trips.list"} 45.5
```

---

#### GET /api/v1/monitoring/metrics

Alternative monitoring endpoint with JSON format (uses custom auth middleware).

**Authentication**: Required (custom auth middleware)  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "cache": {
      "hitRate": {
        "overall": 0.92,
        "memory": 0.65,
        "redis": 0.35
      },
      "memory": {
        "entries": 150,
        "memoryHits": 8500,
        "redisHits": 3500,
        "misses": 2000
      }
    },
    "process": {
      "uptime": 3600,
      "memory": {
        "rss": *********,
        "heapTotal": *********,
        "heapUsed": *********
      },
      "cpu": {
        "user": 123456,
        "system": 789012
      },
      "pid": 1234,
      "nodeVersion": "v18.17.0"
    },
    "system": {
      "loadAverage": [1.5, 1.2, 0.8],
      "freeMemory": **********,
      "totalMemory": **********,
      "platform": "darwin"
    },
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

---

#### GET /api/v1/monitoring/health

Simplified health check (uses custom auth middleware).

**Authentication**: Required (custom auth middleware)  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "memory": {
      "used": *********,
      "total": *********,
      "percentage": 50
    }
  }
}
```

---

#### GET /api/v1/monitoring/usage

Get usage metrics for all AI services.

**Authentication**: Required (custom auth middleware)  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "aiUsage": {
      "gemini": {
        "requests": 1500,
        "tokens": 750000,
        "cost": 0
      },
      "deepseek": {
        "requests": 300,
        "tokens": 150000,
        "cost": 0
      },
      "openrouter": {
        "requests": 50,
        "tokens": 25000,
        "cost": 0.0013
      }
    },
    "totalRequests": 1850,
    "totalTokens": 925000,
    "totalCost": 0.0013,
    "period": "24h"
  }
}
```

---

#### GET /api/v1/monitoring/performance

Get performance metrics.

**Authentication**: Required (custom auth middleware)  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "responseTime": {
      "avg": 45.2,
      "p50": 35,
      "p95": 120,
      "p99": 250
    },
    "throughput": {
      "requestsPerSecond": 15.5,
      "successRate": 0.995
    },
    "errorRate": 0.005,
    "slowQueries": 15,
    "period": "1h"
  }
}
```

---

#### GET /api/v1/monitoring/alerts

Check for usage alerts.

**Authentication**: Required (custom auth middleware)  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "alerts": [
      {
        "type": "usage_high",
        "severity": "warning",
        "message": "AI usage at 80% of daily limit",
        "threshold": 0.8,
        "current": 0.82,
        "timestamp": "2024-01-01T00:00:00.000Z"
      }
    ],
    "hasAlerts": true
  }
}
```

---

#### POST /api/v1/monitoring/reset-counters

Reset daily counters (admin only).

**Authentication**: Required (custom auth middleware, admin check)  
**Rate Limit**: None  
**Admin Check**: User email must contain '@mmkdev.com'

**Response**:

```json
{
  "success": true,
  "data": {
    "message": "Counters reset successfully"
  }
}
```

### Model Configuration Routes

#### GET /api/v1/models/ai

Get available AI models for parsing.

**Authentication**: Not required  
**Rate Limit**: Standard API limits

**Response**:

```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "gemini-2.0-flash-exp",
        "name": "Gemini Flash 2.0",
        "provider": "google",
        "description": "Fast, high quality, completely free",
        "features": ["fast", "free", "high-quality"],
        "limits": {
          "requestsPerMinute": 60,
          "tokensPerMinute": 1000000
        }
      },
      {
        "id": "deepseek/deepseek-chat-v3-0324:free",
        "name": "DeepSeek Chat v3",
        "provider": "openrouter",
        "description": "Free tier on OpenRouter",
        "features": ["free", "good-quality"],
        "limits": {
          "requestsPerDay": 500
        }
      },
      {
        "id": "google/gemini-2.0-flash-001",
        "name": "Gemini Flash 2.0 (Paid)",
        "provider": "openrouter",
        "description": "Very cheap fallback option",
        "features": ["cheap", "reliable"],
        "pricing": {
          "inputTokens": 0.00005,
          "outputTokens": 0.00005,
          "currency": "USD",
          "per": 1000000
        }
      }
    ],
    "defaultModel": "gemini-2.0-flash-exp",
    "fallbackChain": [
      "gemini-2.0-flash-exp",
      "deepseek/deepseek-chat-v3-0324:free",
      "google/gemini-2.0-flash-001"
    ]
  }
}
```

### Testing Routes

> ⚠️ **Warning**: These endpoints should be disabled in production environments.

#### GET /api/v1/test/supabase-status

Check Supabase database and auth connectivity.

**Authentication**: Not required  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "database": {
      "connected": true,
      "profileCount": 1234,
      "error": null
    },
    "auth": {
      "connected": true,
      "userCount": 1,
      "error": null
    }
  }
}
```

---

#### POST /api/v1/test/create-test-user

Create a test user bypassing normal registration flow.

**Authentication**: Not required  
**Rate Limit**: None

**Request Body** (optional):

```json
{
  "email": "<EMAIL>",
  "password": "Test123@"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "test_user_uuid",
      "email": "<EMAIL>",
      "created_at": "2024-01-01T00:00:00.000Z"
    },
    "session": {
      "access_token": "jwt_token",
      "refresh_token": "refresh_token"
    }
  }
}
```

## Additional Routes

### Root Health Check

#### GET /health

Simple health check at the root level (not under /api/v1).

**Authentication**: Not required  
**Rate Limit**: None

**Response**:

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "service": "travelviz-hub"
  }
}
```

## Middleware Reference

### Authentication Middleware

- `authenticateSupabaseUser`: Validates JWT token and attaches user to request
- `authenticateUser`: Custom auth middleware (used by monitoring routes)

### Validation Middleware

- `validateRequest(schema, source?)`: Validates request data against Zod schema
- `validateTextContent`: Additional validation for text imports
- `validateFileUpload`: Validates uploaded files
- `validateContentLength(limit)`: Enforces request size limits

### Rate Limiting Middleware

- `apiRateLimit`: Standard API rate limiting
- `authRateLimit`: 5 requests per 15 minutes
- `loginRateLimit`: 10 requests per 15 minutes
- `strictRateLimit`: Very restrictive for sensitive operations
- `importRateLimit`: Special limits for import operations
- `placesRateLimit`: 30 requests per minute

### Authorization Middleware

- `verifyTripOwnership`: Ensures user owns the trip
- `verifyActivityOwnership`: Ensures user owns the activity
- `verifyCreateOwnership`: Validates ownership for creation
- `filterOwnedResources(type)`: Filters list results to owned items

### Caching Middleware

- `cacheMiddleware(config)`: Adds caching to GET endpoints
- `cacheInvalidationMiddleware(keys)`: Invalidates cache on mutations

### Performance Middleware

- `performanceMiddleware()`: Tracks request performance metrics

### Request Processing

- `requestIdMiddleware`: Adds unique request ID for tracing
- `handlePayloadTooLarge`: Handles oversized payloads before parsing

## Error Handling

All errors follow a consistent format with appropriate HTTP status codes:

```json
{
  "success": false,
  "error": "Human-readable error message",
  "details": "Additional technical details (optional)",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Common error scenarios:

- Missing required fields: 400 Bad Request
- Invalid authentication: 401 Unauthorized
- Insufficient permissions: 403 Forbidden
- Resource not found: 404 Not Found
- Rate limit exceeded: 429 Too Many Requests
- Server errors: 500 Internal Server Error

## Security Features

1. **Input Sanitization**: Search routes use DOMPurify to sanitize user input
2. **Rate Limiting**: Different limits for different endpoint types
3. **Size Limits**: Request body size limits to prevent DoS
4. **CORS**: Configured for specific origins
5. **Helmet**: Security headers configured
6. **Trust Proxy**: Configured for deployment environments with secure key generation

## Changelog

### Version 2.0.0 (Current)

- Updated all endpoints to use `/api/v1` prefix
- Added search routes documentation
- Added monitoring routes with custom auth
- Documented all middleware
- Added security features section
- Comprehensive endpoint coverage

### Version 1.0.0

- Initial API documentation
- Complete endpoint reference
- Middleware documentation
- Error handling guide

---

Last updated: January 2025
